"""
面向对象场景和UI元素使用示例
展示如何使用新的面向对象设计进行游戏自动化
"""

import sys
import os
from typing import List, Optional

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.modules.perception.perception_module import PerceptionModule
from src.modules.action.action_controller import ActionController
from src.utils.config_loader import ConfigLoader
from src.modules.ui.scene_manager import SceneManager, LegacyCompatibilityAdapter
from src.core.data_structures import GameScene
from src.utils.logger import get_logger


class OOPGameAutomationExample:
    """面向对象游戏自动化示例类"""
    
    def __init__(self):
        self.logger = get_logger("OOPExample")
        
        # 初始化核心模块
        self.perception = PerceptionModule()
        self.action = ActionController()
        self.config_loader = ConfigLoader()
        
        # 初始化场景管理器
        self.scene_manager = SceneManager(
            self.perception,
            self.action,
            self.config_loader
        )
        
        # 兼容性适配器
        self.legacy_adapter = LegacyCompatibilityAdapter(self.scene_manager)
    
    def example_1_basic_navigation(self):
        """示例1: 基本导航操作"""
        self.logger.info("=== 示例1: 基本导航操作 ===")
        
        try:
            # 方法1: 使用场景管理器导航
            success = self.scene_manager.navigate_to_scene(GameScene.PRODUCE_SETUP)
            self.logger.info(f"导航到育成准备界面: {'成功' if success else '失败'}")
            
            # 方法2: 直接使用场景类
            main_menu = self.scene_manager.get_scene(GameScene.MAIN_MENU)
            if main_menu and main_menu.is_current_scene():
                success = main_menu.navigate_to_produce()
                self.logger.info(f"通过场景类导航: {'成功' if success else '失败'}")
            
        except Exception as e:
            self.logger.error(f"基本导航示例失败: {e}")
    
    def example_2_ui_element_interaction(self):
        """示例2: UI元素交互"""
        self.logger.info("=== 示例2: UI元素交互 ===")
        
        try:
            # 获取主菜单场景
            main_menu = self.scene_manager.get_scene(GameScene.MAIN_MENU)
            if not main_menu:
                self.logger.error("无法获取主菜单场景")
                return
            
            # 检查育成按钮是否可见
            produce_button = main_menu.get_ui_element('produce_button')
            if produce_button:
                is_visible = produce_button.is_visible()
                confidence = produce_button.get_confidence()
                position = produce_button.get_position()
                
                self.logger.info(f"育成按钮状态:")
                self.logger.info(f"  可见: {is_visible}")
                self.logger.info(f"  置信度: {confidence:.3f}")
                self.logger.info(f"  位置: {position}")
                
                if is_visible:
                    success = produce_button.click()
                    self.logger.info(f"点击育成按钮: {'成功' if success else '失败'}")
            
        except Exception as e:
            self.logger.error(f"UI元素交互示例失败: {e}")
    
    def example_3_scene_state_monitoring(self):
        """示例3: 场景状态监控"""
        self.logger.info("=== 示例3: 场景状态监控 ===")
        
        try:
            # 获取当前场景信息
            current_scene = self.scene_manager.get_current_scene()
            if current_scene:
                self.logger.info(f"当前场景: {current_scene.scene_type.value}")
                
                # 获取所有可见的UI元素
                visible_elements = current_scene.get_all_visible_elements()
                self.logger.info(f"可见UI元素: {list(visible_elements.keys())}")
                
                # 如果是育成主界面，获取可用行动
                if current_scene.scene_type == GameScene.PRODUCE_MAIN:
                    available_actions = current_scene.get_available_actions()
                    self.logger.info(f"可用行动: {available_actions}")
            
            # 获取所有场景信息
            all_scenes_info = self.scene_manager.get_all_scenes_info()
            for scene_name, info in all_scenes_info.items():
                self.logger.info(f"场景 {scene_name}: 当前={info['is_current']}, "
                               f"可见元素={len(info['visible_elements'])}")
            
        except Exception as e:
            self.logger.error(f"场景状态监控示例失败: {e}")
    
    def example_4_complete_produce_flow(self):
        """示例4: 完整育成流程"""
        self.logger.info("=== 示例4: 完整育成流程 ===")
        
        try:
            # 1. 导航到主菜单
            if not self.scene_manager.navigate_to_scene(GameScene.MAIN_MENU):
                self.logger.error("无法导航到主菜单")
                return
            
            # 2. 进入育成准备界面
            if not self.scene_manager.navigate_to_scene(GameScene.PRODUCE_SETUP):
                self.logger.error("无法导航到育成准备界面")
                return
            
            # 3. 配置育成设置
            produce_setup = self.scene_manager.get_scene(GameScene.PRODUCE_SETUP)
            if produce_setup:
                # 选择偶像
                if produce_setup.select_idol("默认偶像"):
                    self.logger.info("偶像选择成功")
                
                # 选择支援卡
                if produce_setup.select_support_cards(["支援卡1", "支援卡2"]):
                    self.logger.info("支援卡选择成功")
                
                # 开始育成
                if produce_setup.start_produce():
                    self.logger.info("育成开始成功")
                    
                    # 4. 等待进入育成主界面
                    if self.scene_manager.navigate_to_scene(GameScene.PRODUCE_MAIN):
                        self.logger.info("成功进入育成主界面")
                        
                        # 5. 执行一些育成行动
                        self._execute_produce_actions()
            
        except Exception as e:
            self.logger.error(f"完整育成流程示例失败: {e}")
    
    def _execute_produce_actions(self):
        """执行育成行动"""
        produce_main = self.scene_manager.get_scene(GameScene.PRODUCE_MAIN)
        if not produce_main:
            return
        
        # 获取可用行动
        available_actions = produce_main.get_available_actions()
        self.logger.info(f"可用行动: {available_actions}")
        
        # 示例：上一节vocal课程
        if 'vocal_lesson_button' in available_actions:
            if produce_main.take_lesson('vocal'):
                self.logger.info("vocal课程执行成功")
        
        # 示例：休息
        elif 'rest_button' in available_actions:
            if produce_main.take_rest():
                self.logger.info("休息执行成功")
    
    def example_5_legacy_compatibility(self):
        """示例5: 遗留代码兼容性"""
        self.logger.info("=== 示例5: 遗留代码兼容性 ===")
        
        try:
            # 使用兼容性适配器调用旧接口
            success = self.legacy_adapter.click_ui_element_by_scene(
                GameScene.MAIN_MENU, 
                'produce_button'
            )
            self.logger.info(f"兼容性点击: {'成功' if success else '失败'}")
            
            # 使用字符串导航
            success = self.legacy_adapter.navigate_to_scene_legacy('produce_setup')
            self.logger.info(f"兼容性导航: {'成功' if success else '失败'}")
            
        except Exception as e:
            self.logger.error(f"兼容性示例失败: {e}")
    
    def example_6_error_handling_and_recovery(self):
        """示例6: 错误处理和恢复"""
        self.logger.info("=== 示例6: 错误处理和恢复 ===")
        
        try:
            # 尝试点击不存在的UI元素
            main_menu = self.scene_manager.get_scene(GameScene.MAIN_MENU)
            if main_menu:
                non_existent_element = main_menu.get_ui_element('non_existent_button')
                if non_existent_element is None:
                    self.logger.info("正确处理了不存在的UI元素")
            
            # 尝试在错误场景中执行操作
            produce_setup = self.scene_manager.get_scene(GameScene.PRODUCE_SETUP)
            if produce_setup and not produce_setup.is_current_scene():
                success = produce_setup.start_produce()
                self.logger.info(f"在错误场景中执行操作: {'成功' if success else '失败（预期）'}")
            
            # 导航超时处理
            success = self.scene_manager.navigate_to_scene(GameScene.PRODUCE_SETUP, timeout=1.0)
            self.logger.info(f"短超时导航: {'成功' if success else '失败（预期）'}")
            
        except Exception as e:
            self.logger.error(f"错误处理示例失败: {e}")
    
    def run_all_examples(self):
        """运行所有示例"""
        self.logger.info("开始运行面向对象场景和UI元素使用示例")
        
        examples = [
            self.example_1_basic_navigation,
            self.example_2_ui_element_interaction,
            self.example_3_scene_state_monitoring,
            self.example_4_complete_produce_flow,
            self.example_5_legacy_compatibility,
            self.example_6_error_handling_and_recovery
        ]
        
        for i, example in enumerate(examples, 1):
            try:
                example()
                self.logger.info(f"示例{i}执行完成\n")
            except Exception as e:
                self.logger.error(f"示例{i}执行失败: {e}\n")
        
        self.logger.info("所有示例执行完成")


def main():
    """主函数"""
    try:
        # 创建示例实例
        example = OOPGameAutomationExample()
        
        # 运行所有示例
        example.run_all_examples()
        
    except Exception as e:
        logger = get_logger("Main")
        logger.error(f"示例程序执行失败: {e}")


if __name__ == "__main__":
    main()
