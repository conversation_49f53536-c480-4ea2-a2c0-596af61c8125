"""
Dataclass vs 传统Class 性能基准测试
用于量化分析两种实现方式的性能差异
"""

import time
import sys
import gc
import statistics
from dataclasses import dataclass, field
from typing import Optional, Tuple, List, Dict
import tracemalloc


# ============================================================================
# 测试用的类定义
# ============================================================================

# 传统Class实现
class TraditionalUIElementConfig:
    def __init__(self, template_name: str, position: Optional[Tuple[int, int]] = None,
                 confidence_threshold: float = 0.8, timeout: float = 5.0,
                 enabled: bool = True, retry_count: int = 3):
        self.template_name = template_name
        self.position = position
        self.confidence_threshold = confidence_threshold
        self.timeout = timeout
        self.enabled = enabled
        self.retry_count = retry_count
    
    def __repr__(self) -> str:
        return (f"TraditionalUIElementConfig(template_name='{self.template_name}', "
                f"position={self.position}, confidence_threshold={self.confidence_threshold}, "
                f"timeout={self.timeout}, enabled={self.enabled}, retry_count={self.retry_count})")
    
    def __eq__(self, other) -> bool:
        if not isinstance(other, TraditionalUIElementConfig):
            return False
        return (self.template_name == other.template_name and
                self.position == other.position and
                self.confidence_threshold == other.confidence_threshold and
                self.timeout == other.timeout and
                self.enabled == other.enabled and
                self.retry_count == other.retry_count)
    
    def __hash__(self) -> int:
        return hash((self.template_name, self.position, self.confidence_threshold,
                    self.timeout, self.enabled, self.retry_count))


# Dataclass实现
@dataclass
class DataclassUIElementConfig:
    template_name: str
    position: Optional[Tuple[int, int]] = None
    confidence_threshold: float = 0.8
    timeout: float = 5.0
    enabled: bool = True
    retry_count: int = 3


# 带__slots__的Dataclass实现
@dataclass
class SlottedDataclassUIElementConfig:
    __slots__ = ['template_name', 'position', 'confidence_threshold', 'timeout', 'enabled', 'retry_count']
    template_name: str
    position: Optional[Tuple[int, int]] = None
    confidence_threshold: float = 0.8
    timeout: float = 5.0
    enabled: bool = True
    retry_count: int = 3


# 复杂对象测试
class TraditionalComplexConfig:
    def __init__(self, name: str, values: List[float], metadata: Dict[str, str],
                 nested_configs: List['TraditionalUIElementConfig']):
        self.name = name
        self.values = values
        self.metadata = metadata
        self.nested_configs = nested_configs
        self.created_at = time.time()


@dataclass
class DataclassComplexConfig:
    name: str
    values: List[float] = field(default_factory=list)
    metadata: Dict[str, str] = field(default_factory=dict)
    nested_configs: List[DataclassUIElementConfig] = field(default_factory=list)
    created_at: float = field(default_factory=time.time)


# ============================================================================
# 性能测试函数
# ============================================================================

class PerformanceBenchmark:
    """性能基准测试类"""
    
    def __init__(self):
        self.results = {}
    
    def run_benchmark(self, name: str, func, iterations: int = 10000, warmup: int = 1000):
        """运行基准测试"""
        print(f"\n运行基准测试: {name}")
        
        # 预热
        for _ in range(warmup):
            func()
        
        # 垃圾回收
        gc.collect()
        
        # 测试多次取平均值
        times = []
        for _ in range(10):
            start_time = time.perf_counter()
            for _ in range(iterations):
                func()
            end_time = time.perf_counter()
            times.append(end_time - start_time)
        
        avg_time = statistics.mean(times)
        std_dev = statistics.stdev(times) if len(times) > 1 else 0
        
        self.results[name] = {
            'avg_time': avg_time,
            'std_dev': std_dev,
            'iterations': iterations,
            'time_per_op': avg_time / iterations * 1000000  # 微秒
        }
        
        print(f"  平均时间: {avg_time:.6f}秒")
        print(f"  标准差: {std_dev:.6f}秒")
        print(f"  每次操作: {avg_time / iterations * 1000000:.2f}微秒")
    
    def test_instantiation(self):
        """测试实例化性能"""
        print("=== 实例化性能测试 ===")
        
        # 传统Class
        def create_traditional():
            return TraditionalUIElementConfig("test_template", (100, 200), 0.8, 5.0, True, 3)
        
        # Dataclass
        def create_dataclass():
            return DataclassUIElementConfig("test_template", (100, 200), 0.8, 5.0, True, 3)
        
        # Slotted Dataclass
        def create_slotted():
            return SlottedDataclassUIElementConfig("test_template", (100, 200), 0.8, 5.0, True, 3)
        
        self.run_benchmark("传统Class实例化", create_traditional)
        self.run_benchmark("Dataclass实例化", create_dataclass)
        self.run_benchmark("Slotted Dataclass实例化", create_slotted)
    
    def test_equality_comparison(self):
        """测试相等性比较性能"""
        print("\n=== 相等性比较性能测试 ===")
        
        # 创建测试对象
        trad1 = TraditionalUIElementConfig("test", (100, 200), 0.8, 5.0, True, 3)
        trad2 = TraditionalUIElementConfig("test", (100, 200), 0.8, 5.0, True, 3)
        
        data1 = DataclassUIElementConfig("test", (100, 200), 0.8, 5.0, True, 3)
        data2 = DataclassUIElementConfig("test", (100, 200), 0.8, 5.0, True, 3)
        
        slot1 = SlottedDataclassUIElementConfig("test", (100, 200), 0.8, 5.0, True, 3)
        slot2 = SlottedDataclassUIElementConfig("test", (100, 200), 0.8, 5.0, True, 3)
        
        # 测试相等性比较
        def compare_traditional():
            return trad1 == trad2
        
        def compare_dataclass():
            return data1 == data2
        
        def compare_slotted():
            return slot1 == slot2
        
        self.run_benchmark("传统Class相等性比较", compare_traditional)
        self.run_benchmark("Dataclass相等性比较", compare_dataclass)
        self.run_benchmark("Slotted Dataclass相等性比较", compare_slotted)
    
    def test_attribute_access(self):
        """测试属性访问性能"""
        print("\n=== 属性访问性能测试 ===")
        
        # 创建测试对象
        trad_obj = TraditionalUIElementConfig("test", (100, 200), 0.8, 5.0, True, 3)
        data_obj = DataclassUIElementConfig("test", (100, 200), 0.8, 5.0, True, 3)
        slot_obj = SlottedDataclassUIElementConfig("test", (100, 200), 0.8, 5.0, True, 3)
        
        # 测试属性访问
        def access_traditional():
            return (trad_obj.template_name, trad_obj.confidence_threshold, 
                   trad_obj.timeout, trad_obj.enabled)
        
        def access_dataclass():
            return (data_obj.template_name, data_obj.confidence_threshold,
                   data_obj.timeout, data_obj.enabled)
        
        def access_slotted():
            return (slot_obj.template_name, slot_obj.confidence_threshold,
                   slot_obj.timeout, slot_obj.enabled)
        
        self.run_benchmark("传统Class属性访问", access_traditional)
        self.run_benchmark("Dataclass属性访问", access_dataclass)
        self.run_benchmark("Slotted Dataclass属性访问", access_slotted)
    
    def test_memory_usage(self):
        """测试内存使用"""
        print("\n=== 内存使用测试 ===")
        
        # 单个对象内存使用
        trad_obj = TraditionalUIElementConfig("test", (100, 200), 0.8, 5.0, True, 3)
        data_obj = DataclassUIElementConfig("test", (100, 200), 0.8, 5.0, True, 3)
        slot_obj = SlottedDataclassUIElementConfig("test", (100, 200), 0.8, 5.0, True, 3)
        
        print(f"传统Class对象大小: {sys.getsizeof(trad_obj)} + {sys.getsizeof(trad_obj.__dict__)} = {sys.getsizeof(trad_obj) + sys.getsizeof(trad_obj.__dict__)} bytes")
        print(f"Dataclass对象大小: {sys.getsizeof(data_obj)} + {sys.getsizeof(data_obj.__dict__)} = {sys.getsizeof(data_obj) + sys.getsizeof(data_obj.__dict__)} bytes")
        print(f"Slotted Dataclass对象大小: {sys.getsizeof(slot_obj)} bytes")
        
        # 大量对象内存使用
        def measure_memory_usage(create_func, count=10000):
            tracemalloc.start()
            objects = []
            
            for i in range(count):
                obj = create_func(f"test_{i}", (i, i), 0.8, 5.0, True, 3)
                objects.append(obj)
            
            current, peak = tracemalloc.get_traced_memory()
            tracemalloc.stop()
            
            return current, peak, objects
        
        print(f"\n创建10000个对象的内存使用:")
        
        current, peak, _ = measure_memory_usage(TraditionalUIElementConfig)
        print(f"传统Class: 当前={current/1024/1024:.2f}MB, 峰值={peak/1024/1024:.2f}MB")
        
        current, peak, _ = measure_memory_usage(DataclassUIElementConfig)
        print(f"Dataclass: 当前={current/1024/1024:.2f}MB, 峰值={peak/1024/1024:.2f}MB")
        
        current, peak, _ = measure_memory_usage(SlottedDataclassUIElementConfig)
        print(f"Slotted Dataclass: 当前={current/1024/1024:.2f}MB, 峰值={peak/1024/1024:.2f}MB")
    
    def test_complex_objects(self):
        """测试复杂对象性能"""
        print("\n=== 复杂对象性能测试 ===")
        
        # 创建嵌套配置
        nested_configs_trad = [
            TraditionalUIElementConfig(f"nested_{i}", (i, i), 0.8, 5.0, True, 3)
            for i in range(10)
        ]
        
        nested_configs_data = [
            DataclassUIElementConfig(f"nested_{i}", (i, i), 0.8, 5.0, True, 3)
            for i in range(10)
        ]
        
        def create_complex_traditional():
            return TraditionalComplexConfig(
                "complex_config",
                [1.0, 2.0, 3.0, 4.0, 5.0],
                {"key1": "value1", "key2": "value2"},
                nested_configs_trad
            )
        
        def create_complex_dataclass():
            return DataclassComplexConfig(
                "complex_config",
                [1.0, 2.0, 3.0, 4.0, 5.0],
                {"key1": "value1", "key2": "value2"},
                nested_configs_data
            )
        
        self.run_benchmark("复杂传统Class创建", create_complex_traditional, iterations=1000)
        self.run_benchmark("复杂Dataclass创建", create_complex_dataclass, iterations=1000)
    
    def print_summary(self):
        """打印测试总结"""
        print("\n" + "="*60)
        print("性能测试总结")
        print("="*60)
        
        for name, result in self.results.items():
            print(f"{name:30} {result['time_per_op']:8.2f} 微秒/操作")
        
        # 计算相对性能
        if "传统Class实例化" in self.results and "Dataclass实例化" in self.results:
            trad_time = self.results["传统Class实例化"]["time_per_op"]
            data_time = self.results["Dataclass实例化"]["time_per_op"]
            diff_percent = ((data_time - trad_time) / trad_time) * 100
            print(f"\nDataclass实例化相对传统Class: {diff_percent:+.1f}%")
        
        if "传统Class相等性比较" in self.results and "Dataclass相等性比较" in self.results:
            trad_time = self.results["传统Class相等性比较"]["time_per_op"]
            data_time = self.results["Dataclass相等性比较"]["time_per_op"]
            diff_percent = ((data_time - trad_time) / trad_time) * 100
            print(f"Dataclass相等性比较相对传统Class: {diff_percent:+.1f}%")


def main():
    """主函数"""
    print("Dataclass vs 传统Class 性能基准测试")
    print("="*60)
    
    benchmark = PerformanceBenchmark()
    
    # 运行所有测试
    benchmark.test_instantiation()
    benchmark.test_equality_comparison()
    benchmark.test_attribute_access()
    benchmark.test_memory_usage()
    benchmark.test_complex_objects()
    
    # 打印总结
    benchmark.print_summary()
    
    print("\n测试完成!")


if __name__ == "__main__":
    main()
