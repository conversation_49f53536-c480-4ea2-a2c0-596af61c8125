"""
Dataclass vs 传统Class 实际使用示例
展示在Gakumasu-Bot项目中如何选择和使用不同的类定义方式
"""

import time
import sys
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from typing import Optional, Tuple, List, Dict, Any, Callable
from enum import Enum

# 模拟的依赖
class GameScene(Enum):
    MAIN_MENU = "main_menu"
    PRODUCE_SETUP = "produce_setup"
    PRODUCE_MAIN = "produce_main"

def get_logger(name: str):
    """模拟日志器"""
    class MockLogger:
        def info(self, msg): print(f"INFO: {msg}")
        def error(self, msg): print(f"ERROR: {msg}")
        def warning(self, msg): print(f"WARNING: {msg}")
    return MockLogger()


# ============================================================================
# 示例1: 配置类 - 推荐使用Dataclass
# ============================================================================

@dataclass
class UIElementConfig:
    """UI元素配置 - 使用Dataclass的最佳实践"""
    template_name: str
    confidence_threshold: float = 0.8
    timeout: float = 5.0
    retry_count: int = 3
    enabled: bool = True
    position: Optional[Tuple[int, int]] = None
    
    def __post_init__(self):
        """初始化后验证"""
        if not (0 < self.confidence_threshold <= 1.0):
            raise ValueError("confidence_threshold must be between 0 and 1")
        if self.timeout <= 0:
            raise ValueError("timeout must be positive")
        if self.retry_count < 0:
            raise ValueError("retry_count must be non-negative")

@dataclass(frozen=True)
class Position:
    """位置值对象 - 使用不可变Dataclass"""
    x: int
    y: int
    
    def distance_to(self, other: 'Position') -> float:
        """计算到另一个位置的距离"""
        return ((self.x - other.x) ** 2 + (self.y - other.y) ** 2) ** 0.5
    
    def offset(self, dx: int, dy: int) -> 'Position':
        """创建偏移后的新位置"""
        return Position(self.x + dx, self.y + dy)

@dataclass
class MatchResult:
    """匹配结果 - 数据传输对象使用Dataclass"""
    x: int
    y: int
    width: int
    height: int
    confidence: float
    template_name: str
    timestamp: float = field(default_factory=time.time)
    
    @property
    def center_position(self) -> Position:
        """获取中心位置"""
        return Position(self.x + self.width // 2, self.y + self.height // 2)
    
    @property
    def area(self) -> int:
        """获取区域面积"""
        return self.width * self.height


# ============================================================================
# 示例2: 业务逻辑类 - 推荐使用传统Class
# ============================================================================

class BaseUIElement:
    """UI元素基类 - 复杂业务逻辑使用传统Class"""
    
    def __init__(self, config: UIElementConfig, perception_module=None, action_controller=None):
        self.config = config
        self.perception = perception_module
        self.action = action_controller
        self.logger = get_logger(f"UIElement.{self.__class__.__name__}")
        
        # 复杂的状态管理
        self._cache = {}
        self._performance_history = []
        self._last_interaction_time = 0.0
        self._error_count = 0
    
    def is_visible(self) -> bool:
        """检查UI元素是否可见 - 复杂的业务逻辑"""
        try:
            # 缓存检查
            cache_key = f"visible_{self.config.template_name}_{int(time.time())}"
            if cache_key in self._cache:
                return self._cache[cache_key]
            
            # 模拟感知模块调用
            if self.perception:
                match_result = self._find_element()
                result = match_result is not None and match_result.confidence >= self.config.confidence_threshold
            else:
                result = True  # 模拟结果
            
            # 更新缓存
            self._cache[cache_key] = result
            
            # 清理旧缓存
            self._cleanup_cache()
            
            return result
            
        except Exception as e:
            self.logger.error(f"可见性检查失败: {e}")
            self._error_count += 1
            return False
    
    def click(self) -> bool:
        """点击UI元素 - 复杂的业务逻辑"""
        if not self.config.enabled:
            self.logger.warning(f"元素 {self.config.template_name} 已禁用")
            return False
        
        start_time = time.time()
        
        try:
            # 重试逻辑
            for attempt in range(self.config.retry_count):
                if self._attempt_click():
                    self._record_success(start_time)
                    return True
                
                if attempt < self.config.retry_count - 1:
                    time.sleep(0.1 * (attempt + 1))  # 递增延迟
            
            self._record_failure(start_time, "所有重试都失败")
            return False
            
        except Exception as e:
            self._record_failure(start_time, str(e))
            return False
    
    def _find_element(self) -> Optional[MatchResult]:
        """查找UI元素 - 模拟实现"""
        # 模拟查找逻辑
        return MatchResult(100, 200, 50, 30, 0.95, self.config.template_name)
    
    def _attempt_click(self) -> bool:
        """尝试点击 - 模拟实现"""
        # 模拟点击逻辑
        return True
    
    def _record_success(self, start_time: float):
        """记录成功操作"""
        duration = time.time() - start_time
        self._performance_history.append({
            'operation': 'click',
            'success': True,
            'duration': duration,
            'timestamp': time.time()
        })
        self._last_interaction_time = time.time()
    
    def _record_failure(self, start_time: float, error_message: str):
        """记录失败操作"""
        duration = time.time() - start_time
        self._performance_history.append({
            'operation': 'click',
            'success': False,
            'duration': duration,
            'error': error_message,
            'timestamp': time.time()
        })
        self._error_count += 1
    
    def _cleanup_cache(self):
        """清理过期缓存"""
        current_time = int(time.time())
        expired_keys = [key for key in self._cache.keys() 
                       if int(key.split('_')[-1]) < current_time - 5]
        for key in expired_keys:
            del self._cache[key]
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        if not self._performance_history:
            return {}
        
        successes = [h for h in self._performance_history if h['success']]
        failures = [h for h in self._performance_history if not h['success']]
        
        return {
            'total_operations': len(self._performance_history),
            'success_rate': len(successes) / len(self._performance_history),
            'average_duration': sum(h['duration'] for h in self._performance_history) / len(self._performance_history),
            'error_count': len(failures),
            'last_interaction': self._last_interaction_time
        }


class Button(BaseUIElement):
    """按钮类 - 继承复杂业务逻辑"""
    
    def __init__(self, config: UIElementConfig, perception_module=None, action_controller=None):
        super().__init__(config, perception_module, action_controller)
        self.click_behavior = None
    
    def set_click_behavior(self, behavior: Callable[[], bool]):
        """设置点击行为"""
        self.click_behavior = behavior
    
    def click(self) -> bool:
        """按钮特定的点击逻辑"""
        # 调用父类的点击逻辑
        success = super().click()
        
        # 执行特定的点击行为
        if success and self.click_behavior:
            try:
                return self.click_behavior()
            except Exception as e:
                self.logger.error(f"点击行为执行失败: {e}")
                return False
        
        return success


class ProduceButton(Button):
    """育成按钮 - 特定业务逻辑"""
    
    def __init__(self, perception_module=None, action_controller=None):
        config = UIElementConfig(
            template_name="produce_button",
            confidence_threshold=0.8,
            timeout=5.0,
            retry_count=3
        )
        super().__init__(config, perception_module, action_controller)
    
    def click(self) -> bool:
        """育成按钮特定逻辑"""
        self.logger.info("点击育成按钮")
        
        success = super().click()
        if success:
            # 验证导航结果
            return self._verify_navigation()
        return False
    
    def _verify_navigation(self) -> bool:
        """验证是否成功导航到育成准备界面"""
        # 模拟验证逻辑
        time.sleep(1.0)  # 等待场景切换
        
        # 这里应该检查当前场景是否为PRODUCE_SETUP
        self.logger.info("验证导航结果: 成功")
        return True


# ============================================================================
# 示例3: 混合使用策略
# ============================================================================

@dataclass
class SceneConfig:
    """场景配置 - 使用Dataclass"""
    scene_type: GameScene
    ui_elements: Dict[str, UIElementConfig] = field(default_factory=dict)
    timeout_settings: Dict[str, float] = field(default_factory=lambda: {
        'default': 5.0,
        'navigation': 15.0,
        'loading': 30.0
    })
    
    def add_ui_element(self, name: str, config: UIElementConfig):
        """添加UI元素配置"""
        self.ui_elements[name] = config
    
    def get_timeout(self, operation: str) -> float:
        """获取操作超时时间"""
        return self.timeout_settings.get(operation, self.timeout_settings['default'])


class ConfigurableScene:
    """可配置的场景类 - 使用传统Class处理业务逻辑"""
    
    def __init__(self, config: SceneConfig, perception_module=None, action_controller=None):
        self.config = config
        self.perception = perception_module
        self.action = action_controller
        self.ui_elements = {}
        self.logger = get_logger(f"Scene.{self.__class__.__name__}")
        
        # 根据配置初始化UI元素
        self._initialize_ui_elements()
    
    def _initialize_ui_elements(self):
        """根据配置初始化UI元素"""
        for name, element_config in self.config.ui_elements.items():
            if "button" in element_config.template_name:
                self.ui_elements[name] = Button(element_config, self.perception, self.action)
            else:
                self.ui_elements[name] = BaseUIElement(element_config, self.perception, self.action)
    
    def get_ui_element(self, name: str) -> Optional[BaseUIElement]:
        """获取UI元素"""
        return self.ui_elements.get(name)
    
    def is_current_scene(self) -> bool:
        """检查是否为当前场景"""
        # 模拟场景检查逻辑
        return True
    
    def get_all_visible_elements(self) -> List[str]:
        """获取所有可见的UI元素名称"""
        visible_elements = []
        for name, element in self.ui_elements.items():
            if element.is_visible():
                visible_elements.append(name)
        return visible_elements


# ============================================================================
# 示例4: 性能对比演示
# ============================================================================

def performance_comparison_demo():
    """性能对比演示"""
    print("=== 性能对比演示 ===")
    
    # 测试实例化性能
    iterations = 10000
    
    # 传统Class方式
    class TraditionalConfig:
        def __init__(self, name: str, value: float, enabled: bool = True):
            self.name = name
            self.value = value
            self.enabled = enabled
    
    # Dataclass方式
    @dataclass
    class DataclassConfig:
        name: str
        value: float
        enabled: bool = True
    
    # 测试传统Class
    start_time = time.time()
    for i in range(iterations):
        obj = TraditionalConfig(f"test_{i}", i * 0.1, True)
    traditional_time = time.time() - start_time
    
    # 测试Dataclass
    start_time = time.time()
    for i in range(iterations):
        obj = DataclassConfig(f"test_{i}", i * 0.1, True)
    dataclass_time = time.time() - start_time
    
    print(f"传统Class实例化时间 ({iterations}次): {traditional_time:.4f}秒")
    print(f"Dataclass实例化时间 ({iterations}次): {dataclass_time:.4f}秒")
    print(f"性能差异: {((dataclass_time - traditional_time) / traditional_time * 100):+.2f}%")
    
    # 测试内存使用
    traditional_obj = TraditionalConfig("test", 1.0, True)
    dataclass_obj = DataclassConfig("test", 1.0, True)
    
    print(f"\n传统Class内存使用: {sys.getsizeof(traditional_obj.__dict__)} bytes")
    print(f"Dataclass内存使用: {sys.getsizeof(dataclass_obj.__dict__)} bytes")


# ============================================================================
# 示例5: 实际使用演示
# ============================================================================

def usage_demo():
    """实际使用演示"""
    print("\n=== 实际使用演示 ===")
    
    # 1. 创建配置对象（使用Dataclass）
    button_config = UIElementConfig(
        template_name="produce_button",
        confidence_threshold=0.8,
        timeout=5.0,
        retry_count=3
    )
    
    print(f"按钮配置: {button_config}")
    
    # 2. 创建UI元素（使用传统Class）
    produce_button = ProduceButton()
    
    # 3. 测试UI元素功能
    print(f"按钮可见性: {produce_button.is_visible()}")
    print(f"点击结果: {produce_button.click()}")
    
    # 4. 获取性能统计
    stats = produce_button.get_performance_stats()
    print(f"性能统计: {stats}")
    
    # 5. 创建场景配置（使用Dataclass）
    scene_config = SceneConfig(
        scene_type=GameScene.MAIN_MENU,
        ui_elements={
            "produce_button": button_config
        }
    )
    
    # 6. 创建场景（使用传统Class）
    main_menu_scene = ConfigurableScene(scene_config)
    
    # 7. 测试场景功能
    print(f"当前场景: {main_menu_scene.is_current_scene()}")
    print(f"可见元素: {main_menu_scene.get_all_visible_elements()}")
    
    # 8. 演示位置值对象（使用不可变Dataclass）
    pos1 = Position(100, 200)
    pos2 = Position(150, 250)
    
    print(f"位置1: {pos1}")
    print(f"位置2: {pos2}")
    print(f"距离: {pos1.distance_to(pos2):.2f}")
    print(f"偏移后位置: {pos1.offset(10, 20)}")


if __name__ == "__main__":
    performance_comparison_demo()
    usage_demo()
